<?php

use DD\App\CF7\FormProcessor;
use PHPUnit\Framework\TestCase;

class FormProcessorIpTest extends TestCase
{
    private FormProcessor $processor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processor = new FormProcessor();
    }

    protected function tearDown(): void
    {
        // Wyczyść wszystkie zmienne $_SERVER związane z IP
        $ipHeaders = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipHeaders as $header) {
            unset($_SERVER[$header]);
        }

        parent::tearDown();
    }
    
    public function testGetUserIpAddressWithXForwardedFor(): void
    {
        // Symuluj nagłówek X-Forwarded-For
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '***********, ***********';
        $_SERVER['REMOTE_ADDR'] = '********';

        $ip = $this->callPrivateMethod('getUserIpAddress');

        $this->assertEquals('***********', $ip, 'Should return first public IP from X-Forwarded-For');
    }
    
    public function testGetUserIpAddressWithXRealIp(): void
    {
        // Symuluj nagłówek X-Real-IP
        $_SERVER['HTTP_X_REAL_IP'] = '***********';
        $_SERVER['REMOTE_ADDR'] = '********';

        $ip = $this->callPrivateMethod('getUserIpAddress');

        $this->assertEquals('***********', $ip, 'Should return IP from X-Real-IP');
    }
    
    public function testGetUserIpAddressWithRemoteAddr(): void
    {
        // Symuluj tylko REMOTE_ADDR
        $_SERVER['REMOTE_ADDR'] = '***********';

        $ip = $this->callPrivateMethod('getUserIpAddress');

        $this->assertEquals('***********', $ip, 'Should return IP from REMOTE_ADDR');
    }
    
    public function testGetUserIpAddressWithPrivateIp(): void
    {
        // Symuluj prywatny IP
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '***********00';
        $_SERVER['REMOTE_ADDR'] = '***********00';

        $ip = $this->callPrivateMethod('getUserIpAddress');

        $this->assertEquals('***********00', $ip, 'Should return private IP if no public IP available');
    }
    
    public function testGetUserIpAddressWithInvalidIp(): void
    {
        // Symuluj nieprawidłowy IP
        $_SERVER['HTTP_X_FORWARDED_FOR'] = 'invalid-ip';
        $_SERVER['REMOTE_ADDR'] = '***********';

        $ip = $this->callPrivateMethod('getUserIpAddress');

        $this->assertEquals('***********', $ip, 'Should skip invalid IP and use next valid one');
    }
    
    public function testGetUserIpAddressWithNoIp(): void
    {
        // Wyczyść wszystkie zmienne IP (tearDown już to robi, ale dla pewności)
        $ipHeaders = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipHeaders as $header) {
            unset($_SERVER[$header]);
        }

        $ip = $this->callPrivateMethod('getUserIpAddress');

        $this->assertNull($ip, 'Should return null when no IP available');
    }

    private function callPrivateMethod(string $methodName, array $args = [])
    {
        $reflection = new ReflectionClass($this->processor);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($this->processor, $args);
    }
}
