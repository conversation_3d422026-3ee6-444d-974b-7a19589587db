<?php

use DD\App\Models\GetResponseContact;
use PHPUnit\Framework\TestCase;

class GetResponseContactTest extends TestCase
{
    public function testContactWithoutIp(): void
    {
        $contact = new GetResponseContact('<EMAIL>', '<PERSON>');
        $apiArray = $contact->toApiArray();

        $expected = [
            'email' => '<EMAIL>',
            'name' => '<PERSON>'
        ];

        $this->assertEquals($expected, $apiArray, 'Contact without IP should not include ipAddress field');
    }
    
    public function testContactWithIp(): void
    {
        $contact = new GetResponseContact('<EMAIL>', '<PERSON>', '***********');
        $apiArray = $contact->toApiArray();

        $expected = [
            'email' => '<EMAIL>',
            'name' => '<PERSON>',
            'ipAddress' => '***********'
        ];

        $this->assertEquals($expected, $apiArray, 'Contact with <PERSON> should include ipAddress field');
    }
    
    public function testContactWithEmptyIp(): void
    {
        $contact = new GetResponseContact('<EMAIL>', '<PERSON>', '');
        $apiArray = $contact->toApiArray();

        $expected = [
            'email' => '<EMAIL>',
            'name' => 'John Doe'
        ];

        $this->assertEquals($expected, $apiArray, 'Contact with empty IP should not include ipAddress field');
    }
    
    public function testContactOnlyEmail(): void
    {
        $contact = new GetResponseContact('<EMAIL>');
        $apiArray = $contact->toApiArray();

        $expected = [
            'email' => '<EMAIL>'
        ];

        $this->assertEquals($expected, $apiArray, 'Contact with only email should work correctly');
    }
    
    public function testContactEmailAndIp(): void
    {
        $contact = new GetResponseContact('<EMAIL>', null, '***********');
        $apiArray = $contact->toApiArray();

        $expected = [
            'email' => '<EMAIL>',
            'ipAddress' => '***********'
        ];

        $this->assertEquals($expected, $apiArray, 'Contact with email and IP (no name) should work correctly');
    }
}
