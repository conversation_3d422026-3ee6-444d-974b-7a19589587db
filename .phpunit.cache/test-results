{"version": 2, "defects": {"FormProcessorIpTest::testGetUserIpAddressWithXForwardedFor": 8, "FormProcessorIpTest::testGetUserIpAddressWithXRealIp": 8, "FormProcessorIpTest::testGetUserIpAddressWithRemoteAddr": 8, "FormProcessorIpTest::testGetUserIpAddressWithPrivateIp": 8, "FormProcessorIpTest::testGetUserIpAddressWithInvalidIp": 8, "FormProcessorIpTest::testGetUserIpAddressWithNoIp": 8}, "times": {"GetResponseContactTest::testContactWithoutIp": 0, "GetResponseContactTest::testContactWithIp": 0, "GetResponseContactTest::testContactWithEmptyIp": 0, "GetResponseContactTest::testContactOnlyEmail": 0, "GetResponseContactTest::testContactEmailAndIp": 0}}