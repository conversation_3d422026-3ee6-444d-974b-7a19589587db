parameters:
	ignoreErrors:
		-
			message: '#^Call to method scan_form_tags\(\) on an unknown class WPCF7_ContactForm\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/FormProcessor.php

		-
			message: '#^Call to static method get_instance\(\) on an unknown class WPCF7_Submission\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/FormProcessor.php

		-
			message: '#^Parameter \$contact_form of method DD\\App\\CF7\\FormProcessor\:\:processForm\(\) has invalid type WPCF7_ContactForm\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/FormProcessor.php

		-
			message: '#^Access to property \$name on an unknown class WPCF7_FormTag\.$#'
			identifier: class.notFound
			count: 5
			path: includes/CF7/TagHandler.php

		-
			message: '#^Call to method get_class_option\(\) on an unknown class WPCF7_FormTag\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Call to method get_id_option\(\) on an unknown class WPCF7_FormTag\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Call to method invalidate\(\) on an unknown class WPCF7_Validation\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Call to static method get_instance\(\) on an unknown class WPCF7_Submission\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Function wpcf7_add_form_tag not found\.$#'
			identifier: function.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Function wpcf7_format_atts not found\.$#'
			identifier: function.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Parameter \$result of method DD\\App\\CF7\\TagHandler\:\:validateTag\(\) has invalid type WPCF7_Validation\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Parameter \$tag of method DD\\App\\CF7\\TagHandler\:\:formTagHandler\(\) has invalid type WPCF7_FormTag\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Parameter \$tag of method DD\\App\\CF7\\TagHandler\:\:validateTag\(\) has invalid type WPCF7_FormTag\.$#'
			identifier: class.notFound
			count: 1
			path: includes/CF7/TagHandler.php

		-
			message: '#^Path in require_once\(\) "\./wp\-admin/includes/upgrade\.php" is not a file or it does not exist\.$#'
			identifier: requireOnce.fileNotFound
			count: 1
			path: includes/Database/Migration.php

		-
			message: '#^Constant DD_GR_CF7_PLUGIN_URL not found\.$#'
			identifier: constant.notFound
			count: 2
			path: includes/Plugin.php
