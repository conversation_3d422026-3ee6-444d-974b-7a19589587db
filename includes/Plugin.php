<?php

namespace DD\App;

use DD\App\Database\Migration;
use DD\App\Admin\AdminMenu;
use DD\App\CF7\TagHandler;
use DD\App\CF7\FormProcessor;

class Plugin
{
    private static ?Plugin $instance = null;

    public static function getInstance(): Plugin
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
    }

    public function init(): void
    {
        if (!$this->isContactForm7Active()) {
            add_action('admin_notices', [$this, 'showContactForm7Notice']);
            return;
        }

        $this->initializeComponents();
    }

    public function activate(): void
    {
        Migration::createTables();
        flush_rewrite_rules();
    }

    public function deactivate(): void
    {
        flush_rewrite_rules();
    }

    private function isContactForm7Active(): bool
    {
        return class_exists('WPCF7');
    }

    public function showContactForm7Notice(): void
    {
        echo '<div class="notice notice-error"><p>';
        echo __('GetResponse CF7 Integration wymaga aktywnej wtyczki Contact Form 7.', 'dd-gr-cf7');
        echo '</p></div>';
    }

    private function initializeComponents(): void
    {
        new AdminMenu();
        new TagHandler();
        new FormProcessor();
        
        add_action('init', [$this, 'loadTextDomain']);
        add_action('admin_enqueue_scripts', [$this, 'enqueueAdminAssets']);
    }

    public function loadTextDomain(): void
    {
        load_plugin_textdomain(
            'dd-gr-cf7',
            false,
            dirname(plugin_basename(DD_GR_CF7_PLUGIN_FILE)) . '/languages'
        );
    }

    public function enqueueAdminAssets(string $hook): void
    {
        if (strpos($hook, 'getresponse-cf7') === false) {
            return;
        }

        wp_enqueue_style(
            'dd-gr-cf7-admin',
            DD_GR_CF7_PLUGIN_URL . 'assets/css/admin.css',
            [],
            DD_PLUGIN_VERSION
        );

        wp_enqueue_script(
            'dd-gr-cf7-admin',
            DD_GR_CF7_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            DD_PLUGIN_VERSION,
            true
        );

        wp_localize_script('dd-gr-cf7-admin', 'ddGrCf7Ajax', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dd_gr_cf7_nonce'),
            'strings' => [
                'loading' => __('Ładowanie...', 'dd-gr-cf7'),
                'error' => __('Wystąpił błąd. Spróbuj ponownie.', 'dd-gr-cf7'),
                'success' => __('Zapisano pomyślnie.', 'dd-gr-cf7'),
            ]
        ]);
    }
}
