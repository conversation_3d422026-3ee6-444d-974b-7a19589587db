<?php

namespace DD\App\CF7;

use DD\App\Admin\TagManager;
use WPCF7_FormTag;
use WPCF7_Submission;
use WPCF7_Validation;

class TagHandler
{
    public function __construct()
    {
        add_action('wpcf7_init', [$this, 'addFormTag']);
        add_filter('wpcf7_validate_dd_getresponse', [$this, 'validateTag'], 10, 2);
    }

    public function addFormTag(): void
    {
        wpcf7_add_form_tag('dd_getresponse', [$this, 'formTagHandler'], ['name-attr' => true]);
    }

    public function formTagHandler(WPCF7_FormTag $tag): string
    {
        $tagName = $tag->name;

        if (empty($tagName)) {
            return '<p style="color: red;">Błąd: Spróbuj ponownie później. #001</p>';
        }

        $dbTag = TagManager::getTagByName($tagName);
        if (!$dbTag) {
            return '<p style="color: red;">Błąd: Spróbuj ponownie później. #002</p>';
        }

        // Przygotowanie atrybutów dla input checkbox
        $input_atts = [];
        $input_atts['type'] = 'checkbox';
        $input_atts['name'] = $tag->name . '[]'; // CF7 używa tablicy dla checkboxów
        $input_atts['value'] = esc_html($dbTag->consentText);

        // Dodanie ID jeśli zostało określone
        if ($tag->get_id_option()) {
            $input_atts['id'] = $tag->get_id_option();
        }

        $input_atts = wpcf7_format_atts($input_atts);

        // Przygotowanie klas CSS dla głównego kontenera
        $class_attr = 'wpcf7-form-control wpcf7-checkbox';

        // Dodanie klasy walidacji jeśli zgoda jest wymagana
        if ($dbTag->consentRequired) {
            $class_attr .= ' wpcf7-validates-as-required';
        }

        // Dodanie niestandardowych klas jeśli zostały określone
        if ($tag->get_class_option()) {
            $class_attr .= ' ' . $tag->get_class_option();
        }

        // Generowanie HTML w identycznej strukturze jak natywny CF7 checkbox
        $html = sprintf(
            '<span class="wpcf7-form-control-wrap" data-name="%1$s">',
            esc_attr($tag->name)
        );

        $html .= sprintf(
            '<span class="%1$s">',
            esc_attr($class_attr)
        );

        $html .= '<span class="wpcf7-list-item first last">';

        $html .= sprintf(
            '<label><input %1$s><span class="wpcf7-list-item-label">%2$s</span></label>',
            $input_atts,
            esc_html($dbTag->consentText)
        );

        $html .= '</span>'; // zamknięcie wpcf7-list-item
        $html .= '</span>'; // zamknięcie wpcf7-form-control
        $html .= '</span>'; // zamknięcie wpcf7-form-control-wrap

        return $html;
    }

    public function validateTag(WPCF7_Validation $result, WPCF7_FormTag $tag)
    {
        $tagName = $tag->name;

        if (empty($tagName)) {
            return $result;
        }

        $dbTag = TagManager::getTagByName($tagName);
        if (!$dbTag || !$dbTag->consentRequired) {
            return $result;
        }

        $submission = WPCF7_Submission::get_instance();
        if (!$submission) {
            return $result;
        }

        $posted_data = $submission->get_posted_data();

        if (empty($posted_data[$tag->name])) {
            $result->invalidate($tag, 'To pole jest wymagane.');
        }

        return $result;
    }
}
