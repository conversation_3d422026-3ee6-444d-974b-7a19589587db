<?php

namespace DD\App\Api;

use DD\App\Models\GetResponseList;
use DD\App\Models\GetResponseContact;

class GetResponseApiClient
{
    private const API_BASE_URL = 'https://api.getresponse.com/v3';
    
    private string $apiKey;

    public function __construct(string $apiKey)
    {
        $this->apiKey = $apiKey;
    }

    /** @return GetResponseList[] */
    public function getListsList(): array
    {
        $response = $this->makeRequest('GET', '/campaigns');
        
        if (!$response['success']) {
            error_log('GetResponse API Error: ' . $response['error']);
            return [];
        }

        $lists = [];
        foreach ($response['data'] as $campaign) {
            $lists[] = GetResponseList::fromApiResponse($campaign);
        }

        return $lists;
    }

    public function addContactToList(GetResponseContact $contact, string $listId): bool
    {
        $data = array_merge($contact->toApiArray(), [
            'campaign' => ['campaignId' => $listId]
        ]);

        $response = $this->makeRequest('POST', '/contacts', $data);

        if (!$response['success']) {
            $this->logApiError($response['error'], $response['http_code']);
            return false;
        }

        return true;
    }

    /** @return array{success: bool, data?: array, error?: string, http_code?: int} */
    private function makeRequest(string $method, string $endpoint, ?array $data = null): array
    {
        $url = self::API_BASE_URL . $endpoint;
        
        $args = [
            'method' => $method,
            'headers' => [
                'X-Auth-Token' => 'api-key ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'timeout' => 30,
        ];

        if ($data !== null) {
            $args['body'] = wp_json_encode($data);
        }

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            return [
                'success' => false,
                'error' => $response->get_error_message(),
            ];
        }

        $http_code = (int) wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $decoded_body = json_decode($body, true);

        if ($http_code >= 200 && $http_code < 300) {
            return [
                'success' => true,
                'data' => $decoded_body,
            ];
        }

        return [
            'success' => false,
            'error' => $decoded_body['message'] ?? 'Unknown API error',
            'http_code' => $http_code,
        ];
    }

    private function logApiError(string $error, ?int $httpCode = null): void
    {
        $message = "GetResponse API Error: {$error}";
        if ($httpCode !== null) {
            $message .= " (HTTP {$httpCode})";
        }
        
        error_log($message);
    }
}
