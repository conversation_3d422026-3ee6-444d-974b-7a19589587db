<?php

namespace DD\App\Admin;

use DD\App\Admin\ApiKey;
use DD\App\Admin\TagManager;

class AdminMenu
{
    public function __construct()
    {
        add_action('admin_menu', [$this, 'addMenuPages']);
        add_action('admin_init', [$this, 'handleFormSubmissions']);
        add_action('wp_ajax_dd_gr_cf7_get_lists', [$this, 'ajaxGetLists']);
        add_action('wp_ajax_dd_gr_cf7_delete_tag', [$this, 'ajaxDeleteTag']);
    }

    public function addMenuPages(): void
    {
        add_options_page(
            'GetResponse CF7',
            'GetResponse CF7',
            'manage_options',
            'getresponse-cf7',
            [$this, 'renderMainPage']
        );
    }

    public function handleFormSubmissions(): void
    {
        if (!isset($_POST['dd_gr_cf7_nonce']) || !wp_verify_nonce($_POST['dd_gr_cf7_nonce'], 'dd_gr_cf7_action')) {
            return;
        }

        if (isset($_POST['save_api_key'])) {
            $this->handleApiKeySave();
        }

        if (isset($_POST['save_tag'])) {
            $this->handleTagSave();
        }
    }

    private function handleApiKeySave(): void
    {
        if (!current_user_can('manage_options')) {
            return;
        }

        $apiKey = new ApiKey();
        $key = sanitize_text_field($_POST['api_key'] ?? '');
        
        if ($apiKey->saveApiKey($key)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>Klucz API został zapisany.</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>Błąd podczas zapisywania klucza API.</p></div>';
            });
        }
    }

    private function handleTagSave(): void
    {
        if (!current_user_can('manage_options')) {
            return;
        }

        $tagName = sanitize_text_field($_POST['tag_name'] ?? '');
        $listId = sanitize_text_field($_POST['list_id'] ?? '');
        $consentRequired = isset($_POST['consent_required']);
        $consentText = sanitize_textarea_field($_POST['consent_text'] ?? '');
        $tagId = isset($_POST['tag_id']) ? (int) $_POST['tag_id'] : null;

        if (empty($tagName) || empty($listId) || empty($consentText)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>Wszystkie pola są wymagane.</p></div>';
            });
            return;
        }

        if (TagManager::tagNameExists($tagName, $tagId)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>Tag o tej nazwie już istnieje.</p></div>';
            });
            return;
        }

        if ($tagId) {
            $success = TagManager::updateTag($tagId, $tagName, $listId, $consentRequired, $consentText);
        } else {
            $tag = TagManager::createTag($tagName, $listId, $consentRequired, $consentText);
            $success = $tag !== null;
        }

        if ($success) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>Tag został zapisany.</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>Błąd podczas zapisywania tagu.</p></div>';
            });
        }
    }

    public function renderMainPage(): void
    {
        $apiKey = new ApiKey();
        $hasApiKey = $apiKey->hasApiKey();

        // Handle tag editing
        $editTag = null;
        if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
            $editTag = TagManager::getTagById((int) $_GET['edit']);
        }

        $tags = TagManager::getAllTags();
        ?>
        <div class="wrap">
            <h1>GetResponse CF7</h1>

            <!-- API Key Configuration Section -->
            <div class="dd-gr-cf7-admin">
                <div class="tag-form-section">
                    <h2>Konfiguracja API</h2>

                    <form method="post" action="">
                        <?php wp_nonce_field('dd_gr_cf7_action', 'dd_gr_cf7_nonce'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="api_key">Klucz API GetResponse</label>
                                </th>
                                <td>
                                    <input type="text" id="api_key" name="api_key" class="regular-text"/>
                                    <p class="description">
                                        <?php if ($hasApiKey) echo '<span style="color: green;">Klucz API jest ustawiony.</span>'; ?>
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <?php submit_button('Zapisz klucz API', 'primary', 'save_api_key'); ?>
                    </form>
                </div>

                <?php if ($hasApiKey): ?>
                <!-- Tag Management Section -->
                <div class="tag-form-section">
                    <h2><?php echo $editTag ? 'Edytuj tag' : 'Dodaj nowy tag'; ?></h2>

                    <form method="post" action="">
                        <?php wp_nonce_field('dd_gr_cf7_action', 'dd_gr_cf7_nonce'); ?>

                        <?php if ($editTag): ?>
                            <input type="hidden" name="tag_id" value="<?php echo $editTag->id; ?>" />
                        <?php endif; ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="tag_name">Nazwa tagu</label>
                                </th>
                                <td>
                                    <input type="text" id="tag_name" name="tag_name" class="regular-text"
                                           value="<?php echo esc_attr($editTag->tagName ?? ''); ?>" required />
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="list_id">Lista GetResponse</label>
                                </th>
                                <td>
                                    <select id="list_id" name="list_id" class="regular-text" required data-selected="<?php echo esc_attr($editTag->listId ?? ''); ?>">
                                        <option value="">Wybierz listę...</option>
                                    </select>
                                    <button type="button" id="load_lists" class="button">Załaduj listy</button>
                                    <p class="description">Kliknij "Załaduj listy" aby pobrać listy z GetResponse.</p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="consent_required">Zgoda wymagana</label>
                                </th>
                                <td>
                                    <label>
                                        <input type="checkbox" id="consent_required" name="consent_required" value="1"
                                               <?php checked($editTag->consentRequired ?? false); ?> />
                                        Wymagaj zgody użytkownika przed dodaniem do listy
                                    </label>
                                </td>
                            </tr>
                            <tr id="consent_text_row">
                                <th scope="row">
                                    <label for="consent_text">Tekst zgody</label>
                                </th>
                                <td>
                                    <textarea id="consent_text" name="consent_text" class="large-text" rows="3"><?php echo esc_textarea($editTag->consentText ?? ''); ?></textarea>
                                    <p class="description">Tekst, który zostanie wyświetlony użytkownikowi jako checkbox zgody.</p>
                                </td>
                            </tr>
                        </table>

                        <?php submit_button($editTag ? 'Aktualizuj tag' : 'Dodaj tag', 'primary', 'save_tag'); ?>

                        <?php if ($editTag): ?>
                            <a href="<?php echo admin_url('admin.php?page=getresponse-cf7'); ?>" class="button">Anuluj edycję</a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Tags List Section -->
                <div class="tags-list-section">
                    <h2>Istniejące tagi</h2>

                    <?php if (empty($tags)): ?>
                        <p>Brak tagów. Dodaj pierwszy tag powyżej.</p>
                    <?php else: ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th>Nazwa tagu</th>
                                    <th>Kod tagu</th>
                                    <th>Zgoda wymagana</th>
                                    <th>Akcje</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tags as $tag): ?>
                                    <tr>
                                        <td><?php echo esc_html($tag->tagName); ?></td>
                                        <td>
                                            <code>[dd_getresponse <?php echo esc_attr($tag->tagName); ?>]</code>
                                        </td>
                                        <td><?php echo $tag->consentRequired ? 'Tak' : 'Nie'; ?></td>
                                        <td>
                                            <a href="<?php echo admin_url('admin.php?page=getresponse-cf7&edit=' . $tag->id); ?>" class="button button-small">Edytuj</a>
                                            <button type="button" class="button button-small delete-tag" data-tag-id="<?php echo $tag->id; ?>">Usuń</button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="tag-form-section">
                    <h2>Zarządzanie tagami</h2>
                    <p>Aby zarządzać tagami, najpierw skonfiguruj klucz API GetResponse powyżej.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }



    public function ajaxGetLists(): void
    {
        check_ajax_referer('dd_gr_cf7_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $result = TagManager::getGetResponseLists();
        wp_send_json($result);
    }

    public function ajaxDeleteTag(): void
    {
        check_ajax_referer('dd_gr_cf7_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $tagId = (int) ($_POST['tag_id'] ?? 0);
        
        if ($tagId <= 0) {
            wp_send_json_error('Invalid tag ID');
        }

        $success = TagManager::deleteTag($tagId);
        
        if ($success) {
            wp_send_json_success('Tag został usunięty');
        } else {
            wp_send_json_error('Błąd podczas usuwania tagu');
        }
    }
}
